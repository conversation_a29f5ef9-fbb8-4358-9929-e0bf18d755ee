<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络存储技术 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            margin: 30px 0;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            text-align: center;
        }

        .question-title {
            font-size: 2.5em;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .question-content {
            font-size: 1.4em;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-5px);
        }

        .option.correct {
            border-color: #00ff00;
            background: rgba(0, 255, 0, 0.2);
        }

        .option.wrong {
            border-color: #ff0000;
            background: rgba(255, 0, 0, 0.2);
        }

        .learning-section {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .concept-title {
            font-size: 2.2em;
            margin-bottom: 25px;
            text-align: center;
        }

        .storage-demo {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .storage-type {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin: 15px;
            min-width: 250px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .storage-type:hover {
            transform: scale(1.05);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .storage-type.active {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
        }

        .storage-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
        }

        .das-icon { background: #ff6b6b; }
        .nas-icon { background: #4ecdc4; }
        .san-icon { background: #45b7d1; }

        .storage-name {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .storage-desc {
            font-size: 1.1em;
            line-height: 1.6;
        }

        .interactive-canvas {
            width: 100%;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin: 30px 0;
            cursor: pointer;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #ffd700;
            width: 0%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffd700;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .comparison-table th {
            background: rgba(255, 255, 255, 0.2);
            font-weight: bold;
        }

        .highlight {
            background: rgba(255, 215, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .section {
                padding: 20px;
            }
            
            .question-title {
                font-size: 1.8em;
            }
            
            .storage-demo {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 题目展示区域 -->
        <div class="section question-section" id="question-section">
            <h1 class="question-title">网络存储技术选择题</h1>
            <div class="question-content">
                <p><strong>以下关于网络存储的叙述，正确的是（ ）。</strong></p>
            </div>
            <div class="options" id="options">
                <div class="option" data-option="A">
                    <strong>A.</strong> DAS支持完全跨平台文件共享，支持所有的操作系统
                </div>
                <div class="option" data-option="B">
                    <strong>B.</strong> NAS通过SCSI连接至服务器，通过服务器网卡在网络上传输数据
                </div>
                <div class="option" data-option="C">
                    <strong>C.</strong> FC SAN的网络介质为光纤通道，而IP SAN使用标准的以太网
                </div>
                <div class="option" data-option="D">
                    <strong>D.</strong> SAN设备有自己的文件管理系统，NAS中的存储设备没有文件管理系统
                </div>
            </div>
            <div class="explanation" id="answer-explanation" style="display: none;">
                <h3>正确答案：C</h3>
                <p>让我们通过交互式学习来理解为什么！</p>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
        </div>

        <!-- 基础概念学习区域 -->
        <div class="section learning-section" id="concepts-section" style="display: none;">
            <h2 class="concept-title">🎯 网络存储基础概念</h2>
            <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
                点击下方存储类型，开始互动学习！
            </p>

            <div class="storage-demo">
                <div class="storage-type" data-type="das">
                    <div class="storage-icon das-icon">💾</div>
                    <div class="storage-name">DAS</div>
                    <div class="storage-desc">直接附加存储</div>
                </div>
                <div class="storage-type" data-type="nas">
                    <div class="storage-icon nas-icon">🌐</div>
                    <div class="storage-name">NAS</div>
                    <div class="storage-desc">网络附加存储</div>
                </div>
                <div class="storage-type" data-type="san">
                    <div class="storage-icon san-icon">⚡</div>
                    <div class="storage-name">SAN</div>
                    <div class="storage-desc">存储区域网络</div>
                </div>
            </div>

            <canvas class="interactive-canvas" id="storage-canvas"></canvas>

            <div class="explanation" id="concept-explanation">
                <h3>💡 选择一个存储类型开始学习</h3>
                <p>每种存储技术都有其独特的特点和应用场景。点击上方的存储类型，我们将通过动画演示它们的工作原理！</p>
            </div>

            <div class="nav-buttons">
                <button class="btn" id="prev-btn" disabled>← 上一步</button>
                <button class="btn" id="next-btn">下一步 →</button>
            </div>
        </div>

        <!-- 详细对比学习区域 -->
        <div class="section" id="comparison-section" style="display: none;">
            <h2 class="concept-title" style="color: #333;">📊 存储技术详细对比</h2>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>DAS</th>
                        <th>NAS</th>
                        <th>SAN</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>连接方式</strong></td>
                        <td>直接连接服务器</td>
                        <td>通过网络连接</td>
                        <td>专用高速网络</td>
                    </tr>
                    <tr>
                        <td><strong>文件系统</strong></td>
                        <td>依赖服务器</td>
                        <td class="highlight">有自己的文件系统</td>
                        <td>块级存储，无文件系统</td>
                    </tr>
                    <tr>
                        <td><strong>跨平台支持</strong></td>
                        <td class="highlight">不支持跨平台</td>
                        <td>支持跨平台</td>
                        <td>支持跨平台</td>
                    </tr>
                    <tr>
                        <td><strong>网络介质</strong></td>
                        <td>SCSI等直连</td>
                        <td>标准以太网</td>
                        <td class="highlight">光纤通道或以太网</td>
                    </tr>
                </tbody>
            </table>

            <div class="explanation">
                <h3>🔍 关键区别分析</h3>
                <p><strong>为什么选项C是正确的？</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>FC SAN</strong>：使用光纤通道(Fiber Channel)作为网络介质</li>
                    <li><strong>IP SAN</strong>：使用标准以太网进行数据传输</li>
                    <li>这是两种不同的SAN实现方式，题目描述完全正确</li>
                </ul>
            </div>

            <div class="nav-buttons">
                <button class="btn" id="prev-btn-2">← 上一步</button>
                <button class="btn" id="next-btn-2">解题思路 →</button>
            </div>
        </div>

        <!-- 解题思路区域 -->
        <div class="section" id="solution-section" style="display: none;">
            <h2 class="concept-title" style="color: #333;">🧠 解题思路与方法</h2>

            <div class="explanation">
                <h3>📝 逐项分析法</h3>
                <div id="analysis-steps">
                    <div class="analysis-step" data-step="1">
                        <h4>步骤1：分析选项A</h4>
                        <p><strong>DAS支持完全跨平台文件共享，支持所有的操作系统</strong></p>
                        <p class="result">❌ <strong>错误</strong> - DAS依赖服务器，不能提供跨平台文件共享</p>
                    </div>

                    <div class="analysis-step" data-step="2" style="display: none;">
                        <h4>步骤2：分析选项B</h4>
                        <p><strong>NAS通过SCSI连接至服务器，通过服务器网卡在网络上传输数据</strong></p>
                        <p class="result">❌ <strong>错误</strong> - NAS直接通过网络接口连接，不通过SCSI</p>
                    </div>

                    <div class="analysis-step" data-step="3" style="display: none;">
                        <h4>步骤3：分析选项C</h4>
                        <p><strong>FC SAN的网络介质为光纤通道，而IP SAN使用标准的以太网</strong></p>
                        <p class="result">✅ <strong>正确</strong> - 完全符合两种SAN技术的特点</p>
                    </div>

                    <div class="analysis-step" data-step="4" style="display: none;">
                        <h4>步骤4：分析选项D</h4>
                        <p><strong>SAN设备有自己的文件管理系统，NAS中的存储设备没有文件管理系统</strong></p>
                        <p class="result">❌ <strong>错误</strong> - 恰恰相反，NAS有文件系统，SAN是块级存储</p>
                    </div>
                </div>

                <button class="btn" id="step-btn" style="margin: 20px auto; display: block;">开始分析 →</button>
            </div>

            <div class="nav-buttons">
                <button class="btn" id="prev-btn-3">← 上一步</button>
                <button class="btn" id="practice-btn">练习测试 →</button>
            </div>
        </div>

        <!-- 练习测试区域 -->
        <div class="section" id="practice-section" style="display: none;">
            <h2 class="concept-title" style="color: #333;">🎮 互动练习</h2>

            <div class="explanation">
                <h3>🎯 拖拽匹配游戏</h3>
                <p>将左侧的特性拖拽到对应的存储技术上！</p>
            </div>

            <canvas class="interactive-canvas" id="practice-canvas"></canvas>

            <div class="explanation" id="practice-feedback">
                <h3>💡 准备开始</h3>
                <p>拖拽特性卡片到正确的存储技术区域，完成所有匹配后查看结果！</p>
            </div>

            <div class="nav-buttons">
                <button class="btn" id="prev-btn-4">← 上一步</button>
                <button class="btn" id="reset-practice">重新开始</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 0;
        let currentStep = 0;
        let selectedStorage = null;
        let practiceItems = [];
        let practiceComplete = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            setupEventListeners();
            setupCanvases();
        });

        // 初始化动画
        function initializeAnimations() {
            gsap.set('.section', { opacity: 0, y: 50 });
            gsap.to('#question-section', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' });
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 选项点击事件
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', function() {
                    handleOptionClick(this);
                });
            });

            // 存储类型点击事件
            document.querySelectorAll('.storage-type').forEach(type => {
                type.addEventListener('click', function() {
                    handleStorageTypeClick(this);
                });
            });

            // 导航按钮事件
            setupNavigationButtons();

            // 解题步骤按钮
            document.getElementById('step-btn').addEventListener('click', showNextAnalysisStep);

            // 练习重置按钮
            document.getElementById('reset-practice').addEventListener('click', resetPractice);
        }

        // 处理选项点击
        function handleOptionClick(option) {
            const selectedOption = option.dataset.option;

            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记选择结果
            if (selectedOption === 'C') {
                option.classList.add('correct');
                showAnswerExplanation(true);
            } else {
                option.classList.add('wrong');
                showAnswerExplanation(false);
            }

            // 更新进度
            updateProgress(25);
        }

        // 显示答案解释
        function showAnswerExplanation(isCorrect) {
            const explanation = document.getElementById('answer-explanation');
            explanation.style.display = 'block';

            if (isCorrect) {
                explanation.innerHTML = `
                    <h3>🎉 恭喜答对了！</h3>
                    <p><strong>正确答案：C</strong></p>
                    <p>FC SAN确实使用光纤通道，IP SAN使用以太网。让我们深入学习这些概念！</p>
                `;
            } else {
                explanation.innerHTML = `
                    <h3>💡 再想想看</h3>
                    <p><strong>正确答案：C</strong></p>
                    <p>别担心，让我们通过互动学习来理解每种存储技术的特点！</p>
                `;
            }

            gsap.from(explanation, { opacity: 0, y: 20, duration: 0.5 });
        }

        // 处理存储类型点击
        function handleStorageTypeClick(element) {
            const storageType = element.dataset.type;

            // 清除之前的选择
            document.querySelectorAll('.storage-type').forEach(type => {
                type.classList.remove('active');
            });

            // 激活当前选择
            element.classList.add('active');
            selectedStorage = storageType;

            // 显示对应的动画演示
            showStorageAnimation(storageType);

            // 更新解释文本
            updateConceptExplanation(storageType);
        }

        // 显示存储动画
        function showStorageAnimation(type) {
            const canvas = document.getElementById('storage-canvas');
            const ctx = canvas.getContext('2d');

            // 设置canvas尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 根据类型绘制不同的动画
            switch(type) {
                case 'das':
                    drawDASAnimation(ctx, canvas);
                    break;
                case 'nas':
                    drawNASAnimation(ctx, canvas);
                    break;
                case 'san':
                    drawSANAnimation(ctx, canvas);
                    break;
            }
        }

        // DAS动画
        function drawDASAnimation(ctx, canvas) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制服务器
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(centerX - 60, centerY - 40, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('服务器', centerX, centerY);

            // 绘制存储设备
            ctx.fillStyle = '#666';
            ctx.fillRect(centerX + 150, centerY - 30, 80, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('存储', centerX + 190, centerY);

            // 绘制连接线（SCSI）
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX + 60, centerY);
            ctx.lineTo(centerX + 150, centerY);
            ctx.stroke();

            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('SCSI直连', centerX + 105, centerY - 20);
        }

        // NAS动画
        function drawNASAnimation(ctx, canvas) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制网络云
            ctx.fillStyle = '#4ecdc4';
            ctx.beginPath();
            ctx.arc(centerX, centerY - 80, 40, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('网络', centerX, centerY - 75);

            // 绘制NAS设备
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(centerX - 50, centerY, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('NAS', centerX, centerY + 35);

            // 绘制多个客户端
            const clients = [
                { x: centerX - 150, y: centerY + 80, label: '客户端1' },
                { x: centerX, y: centerY + 120, label: '客户端2' },
                { x: centerX + 150, y: centerY + 80, label: '客户端3' }
            ];

            clients.forEach(client => {
                ctx.fillStyle = '#666';
                ctx.fillRect(client.x - 30, client.y - 20, 60, 40);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(client.label, client.x, client.y + 5);

                // 绘制连接线
                ctx.strokeStyle = '#4ecdc4';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(client.x, client.y - 20);
                ctx.lineTo(centerX, centerY + 60);
                ctx.stroke();
            });

            // 网络连接线
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - 40);
            ctx.lineTo(centerX, centerY);
            ctx.stroke();
        }

        // SAN动画
        function drawSANAnimation(ctx, canvas) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制SAN交换机
            ctx.fillStyle = '#45b7d1';
            ctx.fillRect(centerX - 60, centerY - 30, 120, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('SAN交换机', centerX, centerY);

            // 绘制服务器
            ctx.fillStyle = '#666';
            ctx.fillRect(centerX - 150, centerY - 80, 80, 50);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('服务器1', centerX - 110, centerY - 50);

            ctx.fillRect(centerX - 150, centerY + 30, 80, 50);
            ctx.fillText('服务器2', centerX - 110, centerY + 60);

            // 绘制存储设备
            ctx.fillStyle = '#666';
            ctx.fillRect(centerX + 70, centerY - 80, 80, 50);
            ctx.fillText('存储1', centerX + 110, centerY - 50);

            ctx.fillRect(centerX + 70, centerY + 30, 80, 50);
            ctx.fillText('存储2', centerX + 110, centerY + 60);

            // 绘制光纤连接
            const connections = [
                { from: { x: centerX - 70, y: centerY - 55 }, to: { x: centerX - 60, y: centerY - 15 } },
                { from: { x: centerX - 70, y: centerY + 55 }, to: { x: centerX - 60, y: centerY + 15 } },
                { from: { x: centerX + 60, y: centerY - 15 }, to: { x: centerX + 70, y: centerY - 55 } },
                { from: { x: centerX + 60, y: centerY + 15 }, to: { x: centerX + 70, y: centerY + 55 } }
            ];

            ctx.strokeStyle = '#ffd700';
            ctx.lineWidth = 3;
            connections.forEach(conn => {
                ctx.beginPath();
                ctx.moveTo(conn.from.x, conn.from.y);
                ctx.lineTo(conn.to.x, conn.to.y);
                ctx.stroke();
            });

            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('光纤通道/以太网', centerX, centerY - 50);
        }

        // 更新概念解释
        function updateConceptExplanation(type) {
            const explanation = document.getElementById('concept-explanation');
            const explanations = {
                das: {
                    title: '💾 DAS - 直接附加存储',
                    content: `
                        <p><strong>特点：</strong></p>
                        <ul>
                            <li>通过SCSI等接口直接连接服务器</li>
                            <li>依赖服务器的操作系统</li>
                            <li>不支持跨平台文件共享</li>
                            <li>成本低，但扩展性有限</li>
                        </ul>
                        <p><strong>应用场景：</strong>小型企业、个人工作站</p>
                    `
                },
                nas: {
                    title: '🌐 NAS - 网络附加存储',
                    content: `
                        <p><strong>特点：</strong></p>
                        <ul>
                            <li>通过网络直接连接，有自己的操作系统</li>
                            <li>支持跨平台文件共享</li>
                            <li>具有文件管理系统</li>
                            <li>易于管理和扩展</li>
                        </ul>
                        <p><strong>应用场景：</strong>中小企业文件服务器、家庭媒体中心</p>
                    `
                },
                san: {
                    title: '⚡ SAN - 存储区域网络',
                    content: `
                        <p><strong>特点：</strong></p>
                        <ul>
                            <li>专用高速网络连接存储设备</li>
                            <li>块级存储，没有文件系统</li>
                            <li>FC SAN使用光纤通道，IP SAN使用以太网</li>
                            <li>高性能、高可靠性</li>
                        </ul>
                        <p><strong>应用场景：</strong>大型企业、数据中心</p>
                    `
                }
            };

            const info = explanations[type];
            explanation.innerHTML = `<h3>${info.title}</h3>${info.content}`;
            gsap.from(explanation, { opacity: 0, y: 20, duration: 0.5 });
        }

        // 设置导航按钮
        function setupNavigationButtons() {
            // 第一个section的导航
            document.getElementById('next-btn').addEventListener('click', () => {
                showSection('comparison-section');
                updateProgress(50);
            });

            // 第二个section的导航
            document.getElementById('prev-btn-2').addEventListener('click', () => {
                showSection('concepts-section');
            });
            document.getElementById('next-btn-2').addEventListener('click', () => {
                showSection('solution-section');
                updateProgress(75);
            });

            // 第三个section的导航
            document.getElementById('prev-btn-3').addEventListener('click', () => {
                showSection('comparison-section');
            });
            document.getElementById('practice-btn').addEventListener('click', () => {
                showSection('practice-section');
                initializePractice();
                updateProgress(100);
            });

            // 第四个section的导航
            document.getElementById('prev-btn-4').addEventListener('click', () => {
                showSection('solution-section');
            });
        }

        // 显示指定section
        function showSection(sectionId) {
            // 隐藏所有section
            document.querySelectorAll('.section').forEach(section => {
                gsap.to(section, { opacity: 0, y: 50, duration: 0.3, display: 'none' });
            });

            // 显示目标section
            setTimeout(() => {
                const targetSection = document.getElementById(sectionId);
                targetSection.style.display = 'block';
                gsap.to(targetSection, { opacity: 1, y: 0, duration: 0.5, ease: 'power2.out' });

                // 如果是概念section，确保显示初始状态
                if (sectionId === 'concepts-section') {
                    document.getElementById('concept-explanation').innerHTML = `
                        <h3>💡 选择一个存储类型开始学习</h3>
                        <p>每种存储技术都有其独特的特点和应用场景。点击上方的存储类型，我们将通过动画演示它们的工作原理！</p>
                    `;
                }
            }, 300);
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('progress-fill');
            gsap.to(progressFill, { width: percentage + '%', duration: 0.5, ease: 'power2.out' });
        }

        // 显示下一个分析步骤
        function showNextAnalysisStep() {
            currentStep++;
            const stepElement = document.querySelector(`[data-step="${currentStep}"]`);
            const button = document.getElementById('step-btn');

            if (stepElement) {
                stepElement.style.display = 'block';
                gsap.from(stepElement, { opacity: 0, x: -30, duration: 0.5 });

                if (currentStep < 4) {
                    button.textContent = `继续分析 (${currentStep}/4) →`;
                } else {
                    button.textContent = '分析完成 ✓';
                    button.disabled = true;
                }
            }
        }

        // 初始化练习
        function initializePractice() {
            const canvas = document.getElementById('practice-canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // 初始化练习项目
            practiceItems = [
                { text: '直接连接服务器', correct: 'DAS', x: 50, y: 50, dragging: false },
                { text: '有自己的文件系统', correct: 'NAS', x: 50, y: 120, dragging: false },
                { text: '使用光纤通道', correct: 'SAN', x: 50, y: 190, dragging: false },
                { text: '支持跨平台共享', correct: 'NAS', x: 50, y: 260, dragging: false }
            ];

            drawPracticeCanvas(ctx, canvas);
            setupPracticeInteraction(canvas);
        }

        // 绘制练习画布
        function drawPracticeCanvas(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制目标区域
            const targets = [
                { name: 'DAS', x: canvas.width - 200, y: 80, color: '#ff6b6b' },
                { name: 'NAS', x: canvas.width - 200, y: 180, color: '#4ecdc4' },
                { name: 'SAN', x: canvas.width - 200, y: 280, color: '#45b7d1' }
            ];

            targets.forEach(target => {
                ctx.fillStyle = target.color;
                ctx.fillRect(target.x, target.y - 30, 150, 60);
                ctx.fillStyle = 'white';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(target.name, target.x + 75, target.y + 5);
            });

            // 绘制拖拽项目
            practiceItems.forEach(item => {
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(item.x, item.y, 180, 40);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(item.x, item.y, 180, 40);

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(item.text, item.x + 10, item.y + 25);
            });
        }

        // 设置练习交互
        function setupPracticeInteraction(canvas) {
            let dragItem = null;
            let mousePos = { x: 0, y: 0 };

            canvas.addEventListener('mousedown', (e) => {
                const rect = canvas.getBoundingClientRect();
                mousePos.x = e.clientX - rect.left;
                mousePos.y = e.clientY - rect.top;

                // 检查是否点击了某个项目
                practiceItems.forEach(item => {
                    if (mousePos.x >= item.x && mousePos.x <= item.x + 180 &&
                        mousePos.y >= item.y && mousePos.y <= item.y + 40) {
                        dragItem = item;
                        item.dragging = true;
                    }
                });
            });

            canvas.addEventListener('mousemove', (e) => {
                if (dragItem) {
                    const rect = canvas.getBoundingClientRect();
                    dragItem.x = e.clientX - rect.left - 90;
                    dragItem.y = e.clientY - rect.top - 20;

                    const ctx = canvas.getContext('2d');
                    drawPracticeCanvas(ctx, canvas);
                }
            });

            canvas.addEventListener('mouseup', () => {
                if (dragItem) {
                    checkPracticeDrop(dragItem, canvas);
                    dragItem.dragging = false;
                    dragItem = null;
                }
            });
        }

        // 检查练习拖放结果
        function checkPracticeDrop(item, canvas) {
            const targets = [
                { name: 'DAS', x: canvas.width - 200, y: 80 },
                { name: 'NAS', x: canvas.width - 200, y: 180 },
                { name: 'SAN', x: canvas.width - 200, y: 280 }
            ];

            let dropped = false;
            targets.forEach(target => {
                if (item.x + 90 >= target.x && item.x + 90 <= target.x + 150 &&
                    item.y + 20 >= target.y - 30 && item.y + 20 <= target.y + 30) {

                    if (target.name === item.correct) {
                        // 正确匹配
                        item.x = target.x + 10;
                        item.y = target.y - 20;
                        updatePracticeFeedback('正确！' + item.text + ' 属于 ' + target.name);
                    } else {
                        // 错误匹配
                        item.x = 50;
                        item.y = practiceItems.indexOf(item) * 70 + 50;
                        updatePracticeFeedback('再试试看！' + item.text + ' 不属于 ' + target.name);
                    }
                    dropped = true;
                }
            });

            if (!dropped) {
                // 没有放到目标区域，回到原位
                item.x = 50;
                item.y = practiceItems.indexOf(item) * 70 + 50;
            }

            const ctx = canvas.getContext('2d');
            drawPracticeCanvas(ctx, canvas);
        }

        // 更新练习反馈
        function updatePracticeFeedback(message) {
            const feedback = document.getElementById('practice-feedback');
            feedback.innerHTML = `<h3>💡 ${message}</h3>`;
            gsap.from(feedback, { opacity: 0, scale: 0.9, duration: 0.3 });
        }

        // 重置练习
        function resetPractice() {
            currentStep = 0;
            practiceComplete = false;
            initializePractice();
            updatePracticeFeedback('准备开始');
        }

        // 设置画布
        function setupCanvases() {
            // 响应式画布设置
            function resizeCanvases() {
                const canvases = document.querySelectorAll('canvas');
                canvases.forEach(canvas => {
                    canvas.width = canvas.offsetWidth;
                    canvas.height = canvas.offsetHeight;
                });
            }

            window.addEventListener('resize', resizeCanvases);
            resizeCanvases();
        }

        // 初始显示概念学习section
        setTimeout(() => {
            showSection('concepts-section');
        }, 2000);
    </script>
</body>
</html>
